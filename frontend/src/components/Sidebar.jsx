import { Link, useLocation } from 'react-router-dom';
import {
  FaHome,
  FaClock,
  FaStar,
  FaFire,
  FaSync,
  FaCrown,
  FaUsers,
  FaFistRaised,
  FaMap,
  FaGamepad,
  FaGhost,
  FaTimes,
  FaPuzzlePiece,
  FaRunning,
  FaFootballBall,
  FaChess,
  FaCogs,
  FaBullseye
} from 'react-icons/fa';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';
import PropTypes from 'prop-types';
import { getGameCategories } from '../utils/categoryUtils';

const Sidebar = ({ isOpen, onToggle }) => {
  const { t } = useLanguage();
  const location = useLocation();
  const { isInitialized } = useSidebar();

  // Navigation items with their corresponding icons and routes
  const navigationItems = [
    { key: 'home', icon: FaHome, route: '/', color: 'text-blue-400' },
    { key: 'recentlyPlayed', icon: Fa<PERSON><PERSON>, route: '/recently-played', color: 'text-green-400' },
    { key: 'new', icon: FaStar, route: '/new', color: 'text-yellow-400' },
    { key: 'trendingNow', icon: FaFire, route: '/trending', color: 'text-red-400' },
    { key: 'updated', icon: FaSync, route: '/updated', color: 'text-cyan-400' },
    { key: 'originals', icon: FaCrown, route: '/originals', color: 'text-purple-400' },
    { key: 'multiplayer', icon: FaUsers, route: '/multiplayer', color: 'text-pink-400' },
  ];

  // Icon mapping for categories
  const categoryIconMap = {
    action: { icon: FaFistRaised, color: 'text-red-500' },
    adventure: { icon: FaMap, color: 'text-green-500' },
    rpg: { icon: FaChess, color: 'text-purple-500' },
    simulation: { icon: FaCogs, color: 'text-blue-500' },
    strategy: { icon: FaChess, color: 'text-indigo-500' },
    sports: { icon: FaFootballBall, color: 'text-orange-500' },
    puzzle: { icon: FaPuzzlePiece, color: 'text-yellow-500' },
    horror: { icon: FaGhost, color: 'text-gray-600' },
    platformer: { icon: FaRunning, color: 'text-green-400' },
    shooter: { icon: FaBullseye, color: 'text-red-700' },
  };

  // Generate category items from JSON data only
  const categoryItems = getGameCategories().map(category => ({
    key: category.value,
    icon: categoryIconMap[category.value]?.icon || FaGamepad,
    route: `/category/${category.slug}`,
    color: categoryIconMap[category.value]?.color || 'text-gray-400'
  }));

  const isActiveRoute = (route) => {
    return location.pathname === route;
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-64 bg-[#1a1a1a] border-r border-gray-800 z-50 transform ${isInitialized ? 'transition-transform duration-300 ease-in-out' : ''} overflow-y-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:${isOpen ? 'translate-x-0' : '-translate-x-full'}
        [&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar]:hover:w-2
        hover:[&::-webkit-scrollbar]:w-2
        [&::-webkit-scrollbar-track]:bg-transparent
        [&::-webkit-scrollbar-thumb]:bg-gray-600
        [&::-webkit-scrollbar-thumb]:rounded-full
        [&::-webkit-scrollbar-thumb]:hover:bg-gray-500
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <h2 className="text-white font-semibold text-lg">Navigation</h2>
          <button
            onClick={onToggle}
            className="lg:hidden text-gray-400 hover:text-white transition-colors"
            aria-label={t('sidebar.toggle.close')}
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Navigation Items */}
        <nav className="p-4">
          <div className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.route);
              
              return (
                <Link
                  key={item.key}
                  to={item.route}
                  className={`
                    flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group
                    ${isActive 
                      ? 'bg-gray-800 text-white' 
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }
                  `}
                  onClick={() => {
                    // Close sidebar on mobile when item is clicked
                    if (window.innerWidth < 1024) {
                      onToggle();
                    }
                  }}
                >
                  <Icon className={`text-lg ${item.color} group-hover:scale-110 transition-transform`} />
                  <span className="font-medium">{t(`sidebar.navigation.${item.key}`)}</span>
                </Link>
              );
            })}
          </div>

          {/* Divider */}
          <div className="my-4 border-t border-gray-700"></div>

          {/* Category Items */}
          <div className="space-y-1">
            {categoryItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.route);
              
              return (
                <Link
                  key={item.key}
                  to={item.route}
                  className={`
                    flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group
                    ${isActive 
                      ? 'bg-gray-800 text-white' 
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }
                  `}
                  onClick={() => {
                    // Close sidebar on mobile when item is clicked
                    if (window.innerWidth < 1024) {
                      onToggle();
                    }
                  }}
                >
                  <Icon className={`text-lg ${item.color} group-hover:scale-110 transition-transform`} />
                  <span className="font-medium">{t(`sidebar.navigation.${item.key}`)}</span>
                </Link>
              );
            })}
          </div>
        </nav>
      </div>


    </>
  );
};

Sidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onToggle: PropTypes.func.isRequired,
};

export default Sidebar;
